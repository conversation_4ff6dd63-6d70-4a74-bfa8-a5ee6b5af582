local Environment = game["Environment"]
local Players = game["Players"]
local ScriptService = game["ScriptService"]
local Hidden = game["Hidden"]
local PlayerDefaults = game["PlayerDefaults"]

local Settings = {
    Prefix = ":",
    -- Prefix must be a string and cannot be "/" (default: ":")

    FreeAdmin = true,
    -- If set to true, every player in your game will have admin permissions (default: false)

    AdminChatColor = Color.FromHex('#FFFF8800'),
    -- Chat color to separate admins from regular players in chat (default: #303030)

    UpdateNotifierEnabled = true,
    -- Whether or not the model should check if there is a more up-to-date model available (default: true, RECOMMENDED!!)

    WhitelistEnabled = false,
    -- Whether or not whitelist is enabled (default: false)

    ServerlockEnabled = false,
    ServerlockMsg = "no reason given"
    -- Whether or not serverlock is enabled & the msg to show players (defaults: false, "no reason given")
}

script.Parent["DoNotModify_Prefix"].Value = Settings.Prefix

local Properties = {
    ModelVersion = 1.5,
    ModelUptoDate = nil,
    NewModelID = nil,
    HiddenResources = nil,
    Server = {
        PlayerCount = 0,
        CmdExecutions = 0
    },
    Errors = {
        IncorrectPermission = "You don't have the correct permissions to do that!",
        PlayerNotExist = "That player doesn't exist!",
        CantExecuteMultiCmds = "You cannot execute multiple commands at once!",
        PlrNeedsToBeAdmin = "That player is supposed to be admin",
        PlrNeedsToBeNonAdmin = "That player is supposed to be non-admin",
        CmdDisabled = "That command has been disabled by the game developer"
    },
    Cache = {
        UtilityCmds = nil,
        ModCmds = nil,
        FunCmds = nil
    },
    UserModelData = {},
    DevMode = false,
    BoolValues = {"true", "false", "on", "off"},
    CmdTypes = {"utility", "mod", "fun"},
    IsMusicPlaying = false,
    CurrentlyPlayingMusic = nil
}
HiddenResourcesPart = Instance.New('Part')
HiddenResourcesPart.Anchored = true
HiddenResourcesPart.CanCollide = false
HiddenResourcesPart.Size = Vector3.New(0.1, 0.1, 0.1)
HiddenResourcesPart.Color = Color.New(HiddenResourcesPart.Color.r, HiddenResourcesPart.Color.g, HiddenResourcesPart.Color.b, 0)
HiddenResourcesPart.Parent = Hidden
HiddenResourcesPart.Name = "PolyAdminHiddenResources"
Properties.HiddenResources = HiddenResourcesPart

--[[
function CreateFolder(name, part)
    if part then
        part.Anchored = true
        part.CanCollide = false
        part.Size = Vector3.New(0.1, 0.1, 0.1)
        part.Color = Color.New(part.Color.r, part.Color.g, part.Color.b, 255)
        part.Name = name
        part.Parent = Hidden
    else
        part = Instance.New('Part')
        part.Anchored = true
        part.CanCollide = false
        part.Size = Vector3.New(0.1, 0.1, 0.1)
        part.Color = Color.New(part.Color.r, part.Color.g, part.Color.b, 255)
        part.Parent = Properties.HiddenResources
        part.Name = name
    end
end
--]]

for i, v in pairs(script.Parent:GetChildren()) do
    if v:IsA('Tool') or v:IsA('Model') then
        v.Parent = Properties.HiddenResources

        if v.Name == "Jail" then
            for i, v in pairs(v:GetChildren()) do
                if v.Name ~= "Walls" then 
                    v.Color = Color.New(v.Color.r, v.Color.g, v.Color.b, 255)
                end
            end
        end
    end
end

local Admins = {}
-- Example Ban: {user = [USERNAME GOES HERE], id = [USER ID GOES HERE]},
local Bans = {}
--[[
Example Ban:

["Player"] = {
    ["Username"] = "[BANNED PLAYER'S USERNAME GOES HERE]",
    ["UserID"] = "[BANNED PLAYER'S USER ID GOES HERE]", 
    ["Reason"] = "[OPTIONAL REASON FOR BAN GOES HERE]",
    ["Mod"] = "[MODERATOR NAME GOES HERE]"
},
--]]
local Whitelist = {}

-- CmdDictionary, CmdAliases, and CmdLogs should not be edited!
local CmdDictionary
local CmdAliases
local SubCmds
local CmdLogs = {}

Players.PlayerAdded:Connect(function (plr)
    AreJoinConditionsMet(plr)

    Properties.Server.PlayerCount = Properties.Server.PlayerCount + 1
    Properties.UserModelData[plr.Name] = {
        IsJailed = false,
        JailModel = nil,
        HasTitle = false,
        Waypoints = {}
    }

    if Settings.UpdateNotifierEnabled == true and Properties.Server.PlayerCount == 1 then
        Http:Get('https://indexxing.vercel.app/polyadmin-version.json', function (data, err, errmsg)
            if not err then
                local jsonTable = json.parse(data)
                if Properties.ModelVersion >= jsonTable["version"] then
                    Properties.ModelUptoDate = true
                    Properties.NewModelID = jsonTable["modelID"]
                else
                    Properties.ModelUptoDate = false
                end
            else
                print("[ PolyAdmin ] Errr while fetching live version: " .. errmsg)
            end
        end,{})
    elseif Settings.UpdateNotifierEnabled == false and Properties.Server.PlayerCount == 1 then
        Settings.ModelUptoDate = true
    end

    if IsModelAdmin(plr) then
        CMDInfo("", "You're an administrator in this game! " .. 'Run the command "' .. Settings.Prefix .. 'cmds" to get a list of commands!', plr)
        plr.ChatColor = Settings.AdminChatColor
        if IsCreator(plr) then Admin(plr) end

        if Properties.ModelUptoDate == false then
            CMDError("Model Version", "The PolyAdmin version used in this game is out-of-date, please get the new model here: https://polytoria.com/models/" .. Properties.NewModelID, plr)
        end
    end

    plr.Chatted:Connect(function (msg)
        wait(0)
        ExecuteCMD(msg, plr)
    end)

    plr.Respawned:Connect(function ()
        if Properties.UserModelData[plr.Name].IsJailed == true then
            plr.Position = Properties.UserModelData[plr.Name].JailModel.Position
            plr.Rotation = Properties.UserModelData[plr.Name].JailModel.Rotation
        end
    end)
end)

Players.PlayerRemoved:Connect(function (plr)
    Properties.UserModelData[plr.Name] = nil
    Properties.Server.PlayerCount = Properties.Server.PlayerCount - 1
end)

function ExecuteCMD(msg, plr)
    if string.sub(msg, 1, #Settings.Prefix) ~= Settings.Prefix then return end
    Properties.Server.CmdExecutions = Properties.Server.CmdExecutions + 1

    if string.find(msg, "|") then
        local splitBySep = SplitString(msg, "|")

        if IsCreator(plr) == false then
            CMDError("", Properties.Errors.CantExecuteMultiCmds, plr)
            return
        end

        for i, v in pairs(splitBySep) do
            ExecuteCMD(v, plr)
        end
    else
        local msg = string.lower(msg:gsub("<noparse>", ""):gsub("</noparse>", ""))
        local attributes = SplitString(msg)
        local cmd = string.sub(table.remove(attributes,1),#Settings.Prefix+1,-1)
        local cmdf = CmdDictionary[cmd]
        if not cmdf and CmdAliases[cmd] then 
            --[[
            local cmdIsValid = IsCmdValid(cmd, plr, CmdAliases[cmd].requirements, attributes, CmdAliases[cmd].usage)
            if cmdIsValid ~= true then return end
            --if cmdIsValid.value ~= true then return end
            --attributes = IsCmdValid.newAtts
            CmdAliases[cmd].exe(plr, attributes, string.sub(msg, #SplitString(msg)[1] + 1, #msg))
            --]]
            local replaceAtts = SplitString(msg)
            replaceAtts[1] = Settings.Prefix .. CmdAliases[cmd]
            local combineAtts
            for i, v in pairs(replaceAtts) do
                if v == replaceAtts[1] then
                    combineAtts = v
                else
                    combineAtts = combineAtts .. " " .. v1
                end
            end
            ExecuteCMD(replaceAtts, plr)
        elseif FindInTable(SubCmds, cmd)[2] == true then
            local atts = SplitString(msg)
            if atts[2] then
                atts[1] = atts[1] .. "_" .. atts[2]
                table.remove(atts, 2)
                ExecuteCMD(table.concat(atts," ",1,#atts), plr)
            end
            return
        end
        local sufficientPerms = GetPermissionLv(plr)
        --[[
        if cmdf.includesubcmds then
            attributes[1] = attributes[1] .. " " .. attributes[2]
            for i = 1, #attributes, 1 do
                if i == 1 then return end
                attributes[i - 1] = attributes[i]
            end

            for i, v in pairs(attributes) do
                print(v)
            end
        end
        --]]

        local tempPermLv
        if type(cmdf.permlv) ~= "number" and cmdf.permlv == "dynamic" then
            if CreatorIsInGame() then tempPermLv = 2 else tempPermLv = 1 end
        else tempPermLv = cmdf.permlv end

        if sufficientPerms >= tempPermLv and not SubCmds[cmd] then
            if cmdf.enabled == false then CMDError("", Properties.Errors.CmdDisabled, plr) return end
            local cmdIsValid = IsCmdValid(cmd, plr, cmdf.requirements, attributes, cmdf.usage)
            if cmdIsValid ~= true then return end

            logsTable = {
                Username = plr.Name,
                ID = plr.UserID,
                Msg = string.sub(msg,1+#Settings.Prefix,-1),
                Time = math.floor(os.time() + .5),
                Sudo = false
            }

            table.insert(CmdLogs, 1, logsTable)

            cmdf.exe(plr, attributes, string.sub(msg, #SplitString(msg)[1] + 1, #msg))
        else
            CMDError("", "You don't have the correct permissions to do that!", plr)
        end
    end
end

CmdDictionary = {
    cmds = {
        cmdtype = "Utility",
        permlv = 1,
        requirements = {
            {Type = "string",Required = true}
        },
        usage = "cmds [utility . mod . fun]",
        enabled = true,
        exe = function(plr, args, msg)
            local CmdTypeExists = false
            for i, v in pairs(Properties.CmdTypes) do
                if v == string.lower(args[1]) then
                    CmdTypeExists = true
                end
            end
            if CmdTypeExists == false then
                CMDError("", "That command type doesn't exist!", plr)
                return
            end

            --[[
            local ConcatedCmdString = ""
            for i, v in pairs(CmdDictionary) do
                if string.lower(v.cmdtype) == string.lower(args[1]) then
                    if ConcatedCmdString ~= "" then
                        ConcatedCmdString = ConcatedCmdString .. "<br>" .. Settings.Prefix .. v.usage
                    else
                        ConcatedCmdString = Settings.Prefix .. v.usage
                    end
                end
            end
            --]]

            args[1] = string.upper(string.sub(args[1], 1, 1)) .. string.sub(args[1], 2, #args[1])

            local ConcatedCmdString = {}
            if Properties.Cache[args[1] .. "Cmds"] == nil then
                for i, v in pairs(CmdDictionary) do
                    if string.lower(v.cmdtype) == string.lower(args[1]) then
                        table.insert(ConcatedCmdString, v.usage)
                    end
                end
                Properties.Cache[args[1] .. "Cmds"] = ConcatedCmdString
            else
                ConcatedCmdString = Properties.Cache[args[1] .. "Cmds"]
            end

            guiTable = {
                {
                    Key = "cmdType",
                    Type = "string",
                    Value = args[1]
                },
                {
                    Key = "Cmds",
                    Type = "string",
                    Value = json.serialize(ConcatedCmdString)
                }
            }

            GUI("Cmds", plr, "new", guiTable, "plr")
        end
    },

    about = {
        cmdtype = "Utility",
        permlv = 0,
        requirements = {},
        usage = "about",
        enabled = true,
        exe = function(plr, args, msg)
            GUI("About", plr, "new", {}, "plr")
        end
    },

    logs = {
        cmdtype = "Utility",
        permlv = 0,
        requirements = {},
        usage = "logs",
        enabled = true,
        exe = function(plr, args, msg)
            if #CmdLogs > 0 then
                GUI("Logs", plr, "new",{{Type = "string", Key = "Logs", Value = json.serialize(CmdLogs)}}, "plr")
            end
        end
    },

    serverinfo = {
        cmdtype = "Utility",
        permlv = 0,
        requirements = {},
        usage = "serverinfo",
        enabled = true,
        exe = function(plr, args, msg)
            CMDInfo("", "Player Count: " .. Properties.Server.PlayerCount, plr)
            CMDInfo("", "Command Execution(s): " .. Properties.Server.CmdExecutions, plr)
        end
    },

    cmdbar2 = {
        cmdtype = "Utility",
        permlv = 1,
        requirements = {},
        usage = "cmdbar2",
        enabled = true,
        exe = function(plr, args, msg)
            GUI("CmdBar2", plr, "new", {}, "plr")
        end
    },

    admins = {
        cmdtype = "Utility",
        permlv = 0,
        requirements = {},
        usage = "admins",
        enabled = true,
        exe = function(plr, args, msg)
            if Settings.FreeAdmin == false then
                guiTable = {
                    {
                        Key = "Admins",
                        Type = "string",
                        Value = json.serialize(Admins)
                    }
                }
                GUI("Admins", plr, "new", guiTable, "plr")
            else
                CMDError("", "Free Admin is enabled!", plr)
            end
        end
    },

    m = {
        cmdtype = "Utility",
        permlv = 1,
        requirements = {
            {Type = "string",Required = true}
        },
        usage = "m [msg]",
        enabled = true,
        exe = function(plr, args, msg)
            guiTable = {
                {
                    Key = "player",
                    Type = "instance",
                    Value = plr
                },
                {
                    Key = "text",
                    Type = "string",
                    Value = args[1]
                }
            }

            GUI("GUIAnnouncement", "all", "new", guiTable, "string")
        end
    },

    tp = {
        cmdtype = "Utility",
        permlv = 1,
        requirements = {
            {Type = "player",Required = true},
            {Type = "player",Required = true}
        },
        usage = "tp [plr 1] [plr 2]",
        enabled = true,
        exe = function(plr, args, msg)
            local player1 = GetPlayer(args[1], plr)
            local player2 = GetPlayer(args[2], plr)
            if type(player2) == "table" then CMDError("", "Cannot TP player(s) to several players", plr) return end
            
            if player1 then
                if player2 then
                    if type(player1) == "table" then
                        for i, v in pairs(player1) do
                            if v:IsA('Player') then
                                v.Position = player2.Position
                            end
                        end
                    else
                        player1.Position = player2.Position
                    end
                else
                    CMDError("", "Player #2: " .. Properties.Errors.PlayerNotExist, plr)
                end
            else
                CMDError("", "Player #1: " .. Properties.Errors.PlayerNotExist, plr)
            end
        end
    },

    bring = {
        cmdtype = "Utility",
        permlv = 1,
        requirements = {
            {Type = "player",Required = true}
        },
        usage = "bring [plr]",
        enabled = true,
        exe = function(plr, args, msg)
            local player = GetPlayer(args[1], plr)

            if player then
                if type(player) == "table" then
                    for i, v in pairs(player1) do
                        if v:IsA('Player') then
                            v.Position = plr.Position
                        end
                    end
                else
                    player.Position = plr.Position
                end
            else
                CMDError("", Properties.Errors.PlayerNotExist, plr)
            end
        end
    },

    to = {
        cmdtype = "Utility",
        permlv = 1,
        enabled = true,
        requirements = {
            {Type = "player",Required = true}
        },
        usage = "to [plr]",
        exe = function(plr, args, msg)
            local player = GetPlayer(args[1], plr)
            if type(player) == "table" then CMDError("", "Cannot tp to several players", plr) return end

            plr.Position = player.Position
        end
    },

    respawn = {
        cmdtype = "Utility",
        permlv = 1,
        requirements = {
            {Type = "player",Required = false}
        },
        usage = "respawn [plr (optional)]",
        enabled = true,
        exe = function(plr, args, msg)
            if not args[1] then args[1] = tostring(plr.Name) end
            local player = GetPlayer(args[1], plr)
            if not player then CMDError("", Properties.Errors.PlayerNotExist, plr) return end

            if type(args[1]) == "table" then
                for i, v in pairs(args[1]) do
                    if v:IsA('Player') then
                        local OldRespawnTime = v.RespawnTime
                        v.RespawnTime = 0
                        v.Health = 0
                        v.RespawnTime = OldRespawnTime
                    end
                end
            else
                local OldRespawnTime = player.RespawnTime
                player.RespawnTime = 0
                player.Health = 0
                player.RespawnTime = OldRespawnTime
            end
        end
    },

    damage = {
        cmdtype = "Utility",
        permlv = 1,
        requirements = {
            {Type = "player",Required = true},
            {Type = "number",Required = true}
        },
        usage = "damage [plr] [amount]",
        enabled = true,
        exe = function(plr, args, msg)
            local player = GetPlayer(args[1], plr)

            if type(player) == "table" then
                for i, v in pairs(player) do
                    if v:IsA('Player') then
                        v.Health = v.Health - tonumber(args[2])
                    end
                end
            else
                player.Health = player.Health - tonumber(args[2])
            end
        end
    },

    kill = {
        cmdtype = "Utility",
        permlv = 1,
        requirements = {
            {Type = "player",Required = true}
        },
        usage = "kill [plr]",
        enabled = true,
        exe = function(plr, args, msg)
            local player = GetPlayer(args[1], plr)

            if type(player) == "table" then
                for i, v in pairs(player) do
                    if v:IsA('Player') then
                        v.Health = 0
                    end
                end
            else
                player.Health = 0
            end
        end
    },

    jail = {
        cmdtype = "Utility",
        permlv = 1,
        requirements = {
            {Type = "player",Required = true}
        },
        usage = "jail [plr]",
        enabled = true,
        exe = function(plr, args, msg)
            local player = GetPlayer(args[1], plr)

            if type(args[1]) == "table" then
                for i, v in pairs(args[1]) do
                    if v:IsA('Player') then
                        v.CanMove = false
                        Properties.UserModelData[v.Name].IsJailed = true

                        local newJailModel = Properties.HiddenResources:FindChild('Jail'):Clone()
                        newJailModel.Parent = Environment
                        newJailModel.Position = Vector3.New(v.Position.x, v.Position.y + 1, v.Position.z)
                        newJailModel.Rotation = v.Rotation
                        Properties.UserModelData[v.Name].JailModel = newJailModel
                    end
                end
            else
                player.CanMove = false
                Properties.UserModelData[player.Name].IsJailed = true

                local newJailModel = Properties.HiddenResources:FindChild('Jail'):Clone()
                newJailModel.Parent = Environment
                newJailModel.Position = Vector3.New(player.Position.x, player.Position.y + 1, player.Position.z)
                newJailModel.Rotation = player.Rotation
                Properties.UserModelData[player.Name].JailModel = newJailModel
            end
        end
    },

    unjail = {
        cmdtype = "Utility",
        permlv = 1,
        requirements = {
            {Type = "player",Required = false}
        },
        usage = "unjail [plr (optional)]",
        enabled = true,
        exe = function(plr, args, msg)
            if not args[1] then args[1] = tostring(plr.Name) end
            local player = GetPlayer(args[1], plr)
            if not player then CMDError("", Properties.Errors.PlayerNotExist, plr) return end
            
            if type(player) == "table" then
                for i, v in pairs(player) do
                    if v:IsA('Player') then
                        if Properties.UserModelData[v.Name].IsJailed == true then
                            v.CanMove = true
                            Properties.UserModelData[v.Name].IsJailed = false
                            Properties.UserModelData[v.Name].JailModel:Destroy()
                            Properties.UserModelData[v.Name].JailModel = nil
                        end
                    end
                end
            else
                if Properties.UserModelData[player.Name].IsJailed == true then
                    player.CanMove = true
                    Properties.UserModelData[player.Name].IsJailed = false
                    Properties.UserModelData[player.Name].JailModel:Destroy()
                    Properties.UserModelData[player.Name].JailModel = nil
                end
            end
        end
    },

    sword = {
        cmdtype = "Utility",
        permlv = 1,
        requirements = {
            {Type = "player",Required = false}
        },
        usage = "sword [plr (optional)]",
        enabled = true,
        exe = function(plr, args, msg)
            if not args[1] then args[1] = tostring(plr.Name) end
            local player = GetPlayer(args[1], plr)
            if not player then CMDError("", Properties.Errors.PlayerNotExist, plr) return end

            if type(player) == "table" then
                for i, v in pairs(player) do
                    if v:IsA('Player') then
                        Properties.HiddenResources:FindChild('Sword'):Clone().Parent = v["Backpack"]
                    end
                end
            else
                Properties.HiddenResources:FindChild('Sword'):Clone().Parent = player["Backpack"]
            end
        end
    },

    health = {
        cmdtype = "Utility",
        permlv = 1,
        requirements = {
            {Type = "player",Required = true},
            {Type = "number",Required = true}
        },
        usage = "health [plr] [health]",
        enabled = true,
        exe = function(plr, args, msg)
            local player = GetPlayer(args[1], plr)

            if type(player) == "table" then
                for i, v in pairs(player) do
                    if v:IsA('Player') then
                        v.Health = tonumber(args[2])
                    end
                end
            else
                player.Health = tonumber(args[2])
            end
        end
    },

    heal = {
        cmdtype = "Utility",
        permlv = 1,
        requirements = {
            {Type = "player",Required = false}
        },
        usage = "heal [plr (optional)]",
        enabled = true,
        exe = function(plr, args, msg)
            if not args[1] then args[1] = tostring(plr.Name) end
            local player = GetPlayer(args[1], plr)
            if not player then CMDError("", Properties.Errors.PlayerNotExist, plr) return end

            if type(player) == "table" then
                for i, v in pairs(player) do
                    if v:IsA('Player') then
                        v.Health = v.MaxHealth
                    end
                end
            else
                player.Health = player.MaxHealth
            end
        end
    },

    forcefield = {
        cmdtype = "Utility",
        permlv = 1,
        requirements = {
            {Type = "player",Required = false}
        },
        usage = "forcefield [plr (optional)]",
        enabled = true,
        exe = function(plr, args, msg)
            if not args[1] then args[1] = tostring(plr.Name) end
            local player = GetPlayer(args[1], plr)
            if not player then CMDError("", Properties.Errors.PlayerNotExist, plr) return end

            if type(player) == "table" then
                for i, v in pairs(player) do
                    if v:IsA('Player') then
                        v.MaxHealth = 10000000000000000
                        v.Health = v.MaxHealth
                    end
                end
            else
                player.MaxHealth = 10000000000000000
                player.Health = player.MaxHealth
            end
        end
    },

    unforcefield = {
        cmdtype = "Utility",
        permlv = 1,
        requirements = {
            {Type = "player",Required = false}
        },
        usage = "unforcefield [plr (optional)]",
        enabled = true,
        exe = function(plr, args, msg)
            if not args[1] then args[1] = tostring(plr.Name) end
            local player = GetPlayer(args[1], plr)
            if not player then CMDError("", Properties.Errors.PlayerNotExist, plr) return end

            if type(player) == "table" then
                for i, v in pairs(player) do
                    if v:IsA('Player') then
                        v.MaxHealth = PlayerDefaults.MaxHealth
                        v.Health = v.MaxHealth
                    end
                end
            else
                player.MaxHealth = PlayerDefaults.MaxHealth
                player.Health = player.MaxHealth
            end
        end
    },

    reset = {
        cmdtype = "Utility",
        permlv = 1,
        requirements = {
            {Type = "player",Required = true}
        },
        usage = "reset [plr]",
        enabled = false,
        exe = function(plr, args, msg)
            print("coming soon")
        end
    },

    walkspeed = {
        cmdtype = "Utility",
        permlv = 1,
        requirements = {
            {Type = "player",Required = true},
            {Type = "number",Required = true}
        },
        usage = "walkspeed [plr] [speed]",
        enabled = true,
        exe = function(plr, args, msg)
            local player = GetPlayer(args[1], plr)

            if type(player) == "table" then
                for i, v in pairs(player) do
                    v.WalkSpeed = tonumber(args[2])
                end
            else
                player.WalkSpeed = tonumber(args[2])
            end
        end
    },

    jumppower = {
        cmdtype = "Utility",
        permlv = 1,
        requirements = {
            {Type = "player",Required = true},
            {Type = "number",Required = true}
        },
        usage = "jumppower [plr] [power]",
        enabled = true,
        exe = function(plr, args, msg)
            local player = GetPlayer(args[1], plr)

            if type(player) == "table" then
                for i, v in pairs(player) do
                    v.JumpPower = tonumber(args[2])
                end
            else
                player.JumpPower = tonumber(args[2])
            end
        end
    },

    maxhealth = {
        cmdtype = "Utility",
        permlv = 0,
        requirements = {
            {Type = "player",Required = true},
            {Type = "number",Required = true}
        },
        usage = "maxhealth [plr] [health]",
        enabled = true,
        exe = function(plr, args, msg)
            local player = GetPlayer(args[1], plr)

            if type(player) == "table" then
                for i, v in pairs(player) do
                    v.MaxHealth = tonumber(args[2])
                end
            else
                player.MaxHealth = tonumber(args[2])
            end
        end
    },

    -- more utility cmds

    newwaypoint = {
        cmdtype = "Utility",
        permlv = 1,
        requirements = {
            {Type = "string",Required = true}
        },
        usage = "newwaypoint [name]",
        enabled = true,
        exe = function(plr, args, msg)
            if Properties.UserModelData[plr.Name]["Waypoints"][args[1]] then CMDError("newwaypoint", "A waypoint already exists under that name!", plr) return end
            table.insert(Properties.UserModelData[plr.Name]["Waypoints"], 1, {WayName = args[1], Pos = plr.Position})

            CMDSuccess("newwaypoint", 'Successfully created waypoint "' .. args[1] .. '"', plr)
        end
    },

    delwaypoint = {
        cmdtype = "Utility",
        permlv = 1,
        requirements = {
            {Type = "string",Required = true}
        },
        usage = "delwaypoint [name]",
        enabled = true,
        exe = function(plr, args, msg)
            Way = nil
            for i, v in pairs(Properties.UserModelData[plr.Name]["Waypoints"]) do
                if v.WayName == args[1] then Way = v end
            end
            if Way == nil then
                CMDError("delwaypoint", "That waypoint doesn't exist!", plr)
                return
            end
            Properties.UserModelData[plr.Name]["Waypoints"][Way] = nil
        end
    },

    waypoint = {
        cmdtype = "Utility",
        permlv = 1,
        requirements = {
            {Type = "string",Required = true},
            {Type = "player",Required = false}
        },
        usage = "waypoint [name] [plr (optional)]",
        enabled = true,
        exe = function(plr, args, msg)
            Way = nil
            for i, v in pairs(Properties.UserModelData[plr.Name]["Waypoints"]) do
                if v.WayName == args[1] then Way = v end
            end
            if Way == nil then
                CMDError("waypoint", "That waypoint doesn't exist!", plr)
                return
            end
            if not args[2] then args[2] = tostring(plr.Name) end
            local player = GetPlayer(args[2], plr)
            if not player then CMDError("", Properties.Errors.PlayerNotExist, plr) return end

            if player and type(player) == "table" then
                for i, v in pairs(player) do
                    v.Position = Way.Pos
                end
            else
                player.Position = Way.Pos
            end
        end
    },

    kick = {
        cmdtype = "Mod",
        permlv = 1,
        requirements = {
            {Type = "player",Required = true},
            {Type = "string",Required = false}
        },
        usage = "kick [plr] [reason (optional)]",
        enabled = true,
        exe = function(plr, args, msg)
            if not args[2] then args[2] = "no reason given" end
            local player = GetPlayer(args[1], plr)

            if player then
                if player == plr and Properties.DevMode == false then
                    CMDError("kick", 'You cannot kick yourself! Correct Usage: "' .. Settings.Prefix .. 'kick [plr] [reason (optional)]"', plr)
                    return
                end

                kickTable = {
                    title = "Kick",
                    title2 = "kicked",
                    type = "kick",
                    reason = GetTextArg(args, 2, #args),
                    executionerName = plr.Name
                }
                Kick(player, kickTable, plr)
            else
                CMDError("kick", Properties.Errors.PlayerNotExist, plr)
            end
        end
    },

    ban = {
        cmdtype = "Mod",
        permlv = 1,
        requirements = {
            {Type = "player",Required = true},
            {Type = "string",Required = false}
        },
        usage = "ban [plr] [reason (optional)]",
        enabled = true,
        exe = function(plr, args, msg)
            if not args[2] then args[2] = "no reason given" end
            local player = GetPlayer(args[1], plr)
                
            if player then
                if player == plr and Properties.DevMode == false then
                    CMDError("ban", 'You cannot ban yourself! Correct Usage: "' .. Settings.Prefix .. 'ban [plr] [reason (optional)]"', plr)
                    return
                end

                local banTable = {
                    ["Ban"] = {
                        ["Username"] = player.Name,
                        ["UserID"] = player.UserID, 
                        ["Reason"] = GetTextArg(args, 3, #args),
                        ["Mod"] = plr
                    },
                }

                local banTable2 = {
                    title = "Ban",
                    title2 = "banned",
                    type = "ban",
                    reason = GetTextArg(args, 3, #args),
                    executionerName = plr.Name
                }

                Bans[player.Name] = {["Username"] = player.Name, ["ID"] = player.UserID, ["Reason"] = GetTextArg(args, 3, #args), ["Mod"] = plr}

                Kick(player, banTable2, plr)
                --player:Kick('You have been banned from the server: "' .. string.sub(msg, #args[1] + 1, #msg) .. '".')
            else
                CMDError("ban", Properties.Errors.PlayerNotExist, plr)
            end
        end
    },

    unban = {
        cmdtype = "Mod",
        permlv = 0,
        requirements = {
            {Type = "string",Required = true}
        },
        usage = "unban [plr]",
        enabled = true,
        exe = function(plr, args, msg)
            if not Bans[args[2]] then
                CMDError("unban", "That player does not exist or is not currently banned in this server", plr)
                return
            end

            Bans[args[2]] = nil
            CMDSuccess("unban", "That player has been unbanned in this server", plr)
        end
    },

    shutdown = {
        cmdtype = "Mod",
        permlv = "dynamic",
        requirements = {
            {Type = "string",Required = false}
        },
        usage = "shutdown [reason (optional)]",
        enabled = true,
        exe = function(plr, args, msg)
            if not args[1] then args[1] = "no reason given" end
            kickTable = {
                title = "Kick",
                title2 = "kicked",
                type = "kick",
                reason = "server shutdown: " .. GetTextArg(args, 1, #args),
                executionerName = plr.Name
            }
            Kick(Players:GetPlayers(), kickTable, plr)
        end
    },

    serverlock = {
        cmdtype = "Mod",
        permlv = "dynamic",
        requirements = {
            {Type = "bool",Required = true},
            {Type = "string",Required = false}
        },
        usage = "serverlock [LOCKED? true . false] [msg (optional)]",
        enabled = true,
        exe = function(plr, args, msg)
            Settings.ServerlockEnabled = not Settings.ServerlockEnabled
            if args[2] and Settings.ServerlockEnabled == true then
                Settings.ServerlockMsg = GetTextArg(args, 2, #args)
            end
        end
    },

    whitelist_enable = {
        cmdtype = "Mod",
        permlv = "dynamic",
        requirements = {},
        usage = "whitelist enable",
        enabled = true,
        exe = function(plr, args, msg)
            Settings.WhitelistEnabled = true
            CMDSuccess("", "Successfully enabled white-list.", plr)
        end
    },

    whitelist_disable = {
        cmdtype = "Mod",
        permlv = "dynamic",
        requirements = {},
        usage = "whitelist disable",
        enabled = true,
        exe = function(plr, args, msg)
            Settings.WhitelistEnabled = false
            CMDSuccess("", "Successfully disabled white-list.", plr)
        end
    },

    whitelist_list = {
        cmdtype = "Mod",
        permlv = "dynamic",
        requirements = {},
        usage = "whitelist list",
        enabled = true,
        exe = function(plr, args, msg)
            if #Whitelist ~= 0 then
                CMDInfo("", table.concat(Whitelist,", ",1,#Whitelist), plr)
            else
                CMDError("", "There are no white-listed user IDs.", plr)
            end
        end
    },

    whitelist_check = {
        cmdtype = "Mod",
        permlv = "dynamic",
        requirements = {
            {Type="number",Required=true}
        },
        usage = "whitelist check [user ID]",
        enabled = true,
        exe = function(plr, args, msg)
            local i = FindInTable(Whitelist, args[1])
            if i[2] == true then
                CMDInfo("", "The user ID " .. args[1] .. " is white-listed.", plr)
            else
                CMDInfo("", "The user ID " .. args[1] .. "is not white-listed.", plr)
            end
        end
    },

    whitelist_add = {
        cmdtype = "Mod",
        permlv = "dynamic",
        requirements = {
            {Type="number",Required=true}
        },
        usage = "whitelist add [user ID]",
        enabled = true,
        exe = function(plr, args, msg)
            local i = FindInTable(Whitelist, args[1])
            if i[2] == false then
                table.insert(Whitelist,1,args[1])
                CMDSuccess("", "Successfully white-listed the user ID " .. args[1] .. ".", plr)
            else
                CMDError("", "This user ID is already white-listed.", plr)
            end
        end
    },

    whitelist_remove = {
        cmdtype = "Mod",
        permlv = "dynamic",
        requirements = {
            {Type="number",Required=true}
        },
        usage = "whitelist remove [user ID]",
        enabled = true,
        exe = function(plr, args, msg)
            local i = FindInTable(Whitelist, args[1])
            if i[2] == true then
                table.remove(Whitelist,i[2])
                CMDSuccess("", "Successfully un-whitelisted the user ID " .. args[1] .. ".", plr)
            else
                CMDError("", "This user ID is not white-listed.", plr)
            end
        end
    },

    userinfo = {
        cmdtype = "Utility",
        permlv = 1,
        requirements = {
            {Type = "number",Required = true}
        },
        usage = "userinfo [user ID]",
        enabled = true,
        exe = function(plr, args, msg)
            Http:Get('https://api.polytoria.com/v1/users/' .. args[1], function (data, err, errmsg)
                if not err then
                    local jsonTable = json.parse(data)
                    GUI("UserInfo", plr, "new", jsonTable, "plr")
                else
                    print("[ PolyAdmin ] Error while fetching user info for user ID " .. args[1] .. " because \"" .. errmsg .. "\"")
                end
            end,{})
        end
    },

    userid = {
        cmdtype = "Mod",
        permlv = 1,
        requirements = {
            {Type="string",Required=false}
        },
        usage = "userid [plr (optional)]",
        enabled = true,
        exe = function(plr, args, msg)
            if not args[1] then args[1] = tostring(plr.Name) end
            local player = GetPlayer(args[1], plr)
            if not player then CMDError("", Properties.Errors.PlayerNotExist, plr) return end

            if type(player) ~= "table" then
                CMDInfo("", player.Name .. "'s user ID is " .. player.UserID, plr)
            else
                CMDError("", "This command does not support several players at once", plr)
            end
        end
    },

    admin = {
        cmdtype = "Mod",
        permlv = 1,
        requirements = {
            {Type="number",Required=true}
        },
        usage = "admin [user ID]",
        enabled = true,
        exe = function(plr, args, msg)
            local i = FindInTable(Admins, args[1])
            if i[2] == false then
                table.insert(Admins,1,args[1])
                CMDSuccess("", "Successfully made user ID " .. args[1] .. " an administrator!", plr)
            else
                CMDError("", "That user ID is already an administrator.", plr)
            end
        end
    },

    unadmin = {
        cmdtype = "Mod",
        permlv = 0,
        requirements = {
            {Type="number",Required=true}
        },
        usage = "unadmin [user ID]",
        enabled = true,
        exe = function(plr, args, msg)
            local i = FindInTable(Admins, args[1])
            if i[2] == true then
                table.remove(Admins,i[1])
                CMDSuccess("", "Successfully removed user ID " .. args[1] .. "'s administrator permissions!", plr)
            else
                CMDError("", "This user ID is not an administrator.", plr)
            end
        end
    },

    announce = {
        cmdtype = "Utility",
        permlv = 1,
        requirements = {
            {Type = "string",Required = true}
        },
        usage = "announce [msg]",
        enabled = true,
        exe = function(plr, args, msg)
            for i, v in pairs(Players:GetPlayers()) do
                guiTable = {
                    {
                        Key = "player",
                        Type = "instance",
                        Value = plr
                    },

                    {
                        Key = "text",
                        Type = "string",
                        Value = GetTextArg(args, 1, #args)
                    }
                }
                GUI("Announcement", "all", "new", guiTable, "string")
            end
        end
    },

    music_play = {
        cmdtype = "Utility",
        permlv = 1,
        requirements = {
            {Type="number",Required=true},
            {Type="bool",Required=false}
        },
        usage = "music play [sound ID] [LOOP? true . false]",
        enabled = true,
        exe = function(plr, args, msg)
            local NewMusic = Instance.New('Sound')
            NewMusic.SoundID = tonumber(args[1])
            if args[2] then NewMusic.Loop = true end
            NewMusic.Parent = Environment
            Properties.CurrentlyPlayingMusic = NewMusic
            Properties.IsMusicPlaying = true
            NewMusic:Play()
        end
    },

    music_stop = {
        cmdtype = "Utility",
        permlv = 1,
        requirements = {},
        usage = "music stop",
        enabled = true,
        exe = function(plr, args, msg)
            if Properties.IsMusicPlaying == false then
                CMDError("", "No music is currently playing.", plr)
                return
            end
            Properties.CurrentlyPlayingMusic:Destroy()
        end
    },

    volume = {
        cmdtype = "Utility",
        permlv = 1,
        requirements = {
            {Type="number",Required=true,Min=0.5,Max=4}
        },
        usage = "volume [number 0.5-4]",
        enabled = true,
        exe = function(plr, args, msg)
            if Properties.IsMusicPlaying == false then
                CMDError("", "No music is currently playing.", plr)
                return
            end
            print("type: " .. type(args[2]))
            Properties.CurrentlyPlayingMusic.Volume = args[2]
        end
    },

    pitch = {
        cmdtype = "Utility",
        permlv = 1,
        requirements = {
            {Type="number",Required=true,Min=0.1,Max=4.5}
        },
        usage = "pitch [number 0.1-4.5]",
        enabled = true,
        exe = function(plr, args, msg)
            if Properties.IsMusicPlaying == false then
                CMDError("", "No music is currently playing.", plr)
                return
            end
            print("type: " .. type(args[2]))
            Properties.CurrentlyPlayingMusic.Pitch = args[2]
        end
    },

    explode = {
        cmdtype = "Fun",
        permlv = 1,
        requirements = {
            {Type = "player",Required = false}
        },
        usage = "explode [plr (optional)]",
        enabled = true,
        exe = function(plr, args, msg)
            if args[1] == nil then args[1] = tostring(plr.Name) end
            local player = GetPlayer(args[1], plr)
            if not player then CMDError("", Properties.Errors.PlayerNotExist, plr) return end
        
            if type(player) == "table" then
                for i, v in pairs(player) do
                    if v:IsA('Player') then
                        Environment:CreateExplosion(Vector3.New(v.Position.x,v.Position.y,v.Position.z), 15, 1000, false, nil, 0)
                        v.Health = 0
                    end
                end
            else
                Environment:CreateExplosion(Vector3.New(player.Position.x,player.Position.y,player.Position.z), 15, 1000, false, nil, 0)
                v.Health = 0
            end
        end
    },

    blind = {
        cmdtype = "Fun",
        permlv = 1,
        requirements = {
            {Type="string",Required=false}
        },
        usage = "blind [plr (optional)]",
        enabled = true,
        exe = function(plr, args, msg)
            if not args[1] then args[1] = tostring(plr.Name) end
            local player = GetPlayer(args[1], plr)
            if not player then CMDError("", Properties.Errors.PlayerNotExist, plr) return end

            GUI("BlindGUI", player, "new", {}, "plr")
        end
    },

    unblind = {
        cmdtype = "Fun",
        permlv = 1,
        requirements = {
            {Type="string",Required=false}
        },
        usage = "unblind [plr (optional)]",
        enabled = true,
        exe = function(plr, args, msg)
            if not args[1] then args[1] = tostring(plr.Name) end
            local player = GetPlayer(args[1], plr)
            if not player then CMDError("", Properties.Errors.PlayerNotExist, plr) return end

            GUI("BlindGUI", player, "del", {}, "plr")
        end
    },

    -- fling command disabled due to it being buggy
    fling = {
        cmdtype = "Fun",
        permlv = 1,
        requirements = {
            {Type="string",Required=false}
        },
        usage = "fling [plr (optional)]",
        enabled = false,
        exe = function(plr, args, msg)
            if not args[1] then args[1] = tostring(plr.Name) end
            local player = GetPlayer(args[1], plr)
            if not player then CMDError("", Properties.Errors.PlayerNotExist, plr) end

            if type(player) == "table" then
                for i, v in pairs(player) do
                    if v:IsA('Player') then
                        if not v:FindChild('PABodyPosition') then
                            local BodyPosition = Instance.New('BodyPosition')
                            BodyPosition.Name = "PABodyPos"
                            BodyPosition.Parent = v
                            BodyPosition.Force = 100
                            BodyPosition.AcceptanceDistance = 10
                            BodyPosition.TargetPosition = Vector3.New(math.random(-50,500), math.random(-50,500), math.random(-50,500))
                        end
                    end
                end
            else
                if not player:FindChild('PABodyPosition') then
                    local BodyPosition = Instance.New('BodyPosition')
                    BodyPosition.Name = "PABodyPos"
                    BodyPosition.Parent = player
                    BodyPosition.Force = 100
                    BodyPosition.AcceptanceDistance = 10
                    BodyPosition.TargetPosition = Vector3.New(math.random(-50,500), math.random(-50,500), math.random(-50,500))
                end
            end

            wait(5)

            if type(player) == "table" then
                for i, v in pairs(player) do
                    if v:IsA('Player') then
                        local BodyPosition = v:FindChild('PABodyPosition')
                        if BodyPosition then
                            BodyPosition:Destroy()
                        end
                    end
                end
            else
                local BodyPosition = player:FindChild('PABodyPosition')
                if BodyPosition then
                    BodyPosition:Destroy()
                end
            end
        end
    },

    chat = {
        cmdtype = "Fun",
        permlv = 1,
        requirements = {
            {Type="string",Required=true},
            {Type="string",Required=true}
        },
        usage = "chat [plr] [message]",
        enabled = true,
        exe = function(plr, args, msg)
            local player = GetPlayer(args[1], plr)

            if player then
                if type(player) == "table" then
                    for i, v in pairs(player) do
                        if v:IsA('Player') then
                            Chat:BroadcastMessage('*' .. v.Name .. ': ' .. GetTextArg(args, 2, #args))
                        end
                    end
                else
                    Chat:BroadcastMessage('*' .. player.Name .. ': ' .. GetTextArg(args, 2, #args))
                end
            else
                CMDError("", Properties.Errors.PlayerNotExist, plr)
            end
        end
    },

    banish = {
        cmdtype = "Fun",
        permlv = 1,
        requirements = {
            {Type="string",Required=false}
        },
        usage = "banish [plr (optional)]",
        enabled = true,
        exe = function(plr, args, msg)
            if not args[1] then args[1] = tostring(plr.Name) end
            local player = GetPlayer(args[1], plr)
            if not player then CMDError("", Properties.Errors.PlayerNotExist, plr) return end

            if type(player) == "table" then
                for i, v in pairs(player) do
                    if v:IsA('Player') then
                        v.Position = v.Position + Vector3.New(0, 10000, 0)
                    end
                end
            else
                player.Position = player.Position + Vector3.New(0, 10000, 0)
            end
        end
    },

    trip = {
        cmdtype = "Fun",
        permlv = 1,
        requirements = {
            {Type="string",Required=false}
        },
        usage = "trip [plr (optional)]",
        enabled = true,
        exe = function(plr, args, msg)
            if not args[1] then args[1] = tostring(plr.Name) end
            local player = GetPlayer(args[1], plr)
            if not player then CMDError("", Properties.Errors.PlayerNotExist, plr) return end
            
            if type(player) == "table" then
                for i, v in pairs(player) do
                    if v:IsA('Player') then
                        local OldPos = v.Position
                        local OldRot = v.Rotation
                        local OldRespawnTime = v.RespawnTime
                        v.RespawnTime = 1000
                        v.Health = 0
                        wait(3.5)
                        v.Health = v.MaxHealth
                        v.RespawnTime = OldRespawnTime
                        v:Respawn()
                        v.Position = OldPos
                        v.Rotation = OldRot
                    end
                end
            else
                local OldPos = player.Position
                local OldRot = player.Rotation
                local OldRespawnTime = player.RespawnTime
                player.RespawnTime = 1000
                player.Health = 0
                wait(3.5)
                player.Health = player.MaxHealth
                player.RespawnTime = OldRespawnTime
                player:Respawn()
                player.Position = OldPos
                player.Rotation = OldRot
            end
        end
    },

    freeze = {
        cmdtype = "Fun",
        permlv = 1,
        requirements = {
            {Type="string",Required=false}
        },
        usage = "freeze [plr (optional)]",
        enabled = true,
        exe = function(plr, args, msg)
            if not args[1] then args[1] = tostring(plr.Name) end
            local player = GetPlayer(args[1], plr)
            if not player then CMDError("", Properties.Errors.PlayerNotExist, plr) return end

            if type(player) == "table" then
                for i, v in pairs(player) do
                    if v:IsA('Player') then
                        v.CanMove = false
                        if not v:FindChild('PAIceBlock') then
                            local IceBlock = Properties.HiddenResources:FindChild('IceBlock'):Clone()
                            IceBlock.Name = "PAIceBlock"
                            IceBlock.Parent = v
                            IceBlock.Position = v.Position
                            IceBlock.Rotation = v.Rotation
                        end
                    end
                end
            else
                player.CanMove = false
                if not player:FindChild('PAIceBlock') then
                    local IceBlock = Properties.HiddenResources:FindChild('IceBlock'):Clone()
                    IceBlock.Name = "PAIceBlock"
                    IceBlock.Parent = player
                    IceBlock.Position = player.Position
                    IceBlock.Rotation = player.Rotation
                end
            end
        end
    },

    unfreeze = {
        cmdtype = "Fun",
        permlv = 1,
        requirements = {
            {Type="string",Required=false}
        },
        usage = "unfreeze [plr (optional)]",
        enabled = true,
        exe = function(plr, args, msg)
            if not args[1] then args[1] = tostring(plr.Name) end
            local player = GetPlayer(args[1], plr)
            if not player then CMDError("", Properties.Errors.PlayerNotExist) return end

            if type(player) == "table" then
                for i, v in pairs(player) do
                    if v:IsA('Player') then
                        v.CanMove = true
                        if v:FindChild('PAIceBlock') then
                            v:FindChild('PAIceBlock'):Destroy()
                        end
                    end
                end
            else
                player.CanMove = true
                if player:FindChild('PAIceBlock') then
                    player:FindChild('PAIceBlock'):Destroy()
                end
            end
        end
    },

    skydive = {
        cmdtype = "Fun",
        permlv = 1,
        requirements = {
            {Type="string",Required=true},
            {Type="number",Required=false}
        },
        usage = "skydive [plr] [height (optional, default: 100)]",
        enabled = true,
        exe = function(plr, args, msg)
            if not args[2] then args[2] = 100 else args[2] = tonumber(args[2]) end
            local player = GetPlayer(args[1], plr)

            if player then
                if type(player) == "table" then
                    for i, v in pairs(player) do
                        if v:IsA('Player') then
                            v.Position = v.Position + Vector3.New(0, args[2], 0)
                        end
                    end
                else
                    player.Position = player.Position + Vector3.New(0, args[2], 0)
                end
            else
                CMDError("", Properties.Errors.PlayerNotExist, plr)
            end
        end
    },

    devmode = {
        cmdtype = "Utility",
        permlv = 2,
        requirements = {},
        usage = "devmode",
        enabled = true,
        exe = function(plr, args, msg)
            Properties.DevMode = not Properties.DevMode
        end
    },

    rank = {
        cmdtype = "Utility",
        permlv = 0,
        requirements = {
            {Type="player",Required=false}
        },
        usage = "rank [plr (optional)]",
        enabled = true,
        exe = function(plr, args, msg)
            if not args[1] then args[1] = tostring(plr.Name) end
            local player = GetPlayer(args[1], plr)
            if not player then CMDError("", Properties.Errors.PlayerNotExist, plr) return end
            
            userPermLv = GetPermissionLv(player)
            userRank = ""
            if userPermLv == 0 then userRank = "non-admin" end
            if userPermLv == 1 then userRank = "admin" end
            if userPermLv == 2 then userRank = "game creator" end
            
            if player == plr then
                CMDInfo("", "Your rank is " .. userRank .. " (" .. userPermLv .. ").", plr)
            else
                CMDInfo("", player.Name .. "'s rank is " .. userRank .. " (" .. userPermLv .. ").", plr)
            end
        end
    },

    title_set = {
        cmdtype = "Utility",
        permlv = 1,
        requirements = {
            {Type="string",Required=true},
            {Type="string",Required=true}
        },
        usage = "title set [plr] [text]",
        enabled = true,
        exe = function(plr, args, msg)
            local player = GetPlayer(args[1], plr)

            if player then
                if type(player) == "table" then
                    for i, v in pairs(player) do
                        if v:IsA('Player') then
                            if Properties.UserModelData[v.Name]["HasTitle"] == false then
                                Properties.UserModelData[v.Name]["HasTitle"] = true
                                
                                local NewTitle = Instance.New('Text3D')
                                if #GetTextArg(args, 2, #args) > 20 then
                                    NewTitle.Text = string.sub(GetTextArg(args, 2, #args), 1, 20)
                                else
                                    NewTitle.Text = GetTextArg(args, 2, #args)
                                end
                                NewTitle.FontSize = 16
                                NewTitle.FaceCamera = true
                                NewTitle.Name = "PATitle"
                                NewTitle.Parent = v
                                NewTitle.Position = v.Position + Vector3.New(0, 4.5, 0)
                            else
                                v:FindChild('PATitle').Text = GetTextArg(args, 2, #args)
                            end
                        end
                    end
                else
                    if Properties.UserModelData[player.Name]["HasTitle"] == false then
                        Properties.UserModelData[player.Name]["HasTitle"] = true
                        
                        local NewTitle = Instance.New('Text3D')
                        if #GetTextArg(args, 2, #args) > 20 then
                            NewTitle.Text = string.sub(GetTextArg(args, 2, #args), 1, 20)
                        else
                            NewTitle.Text = GetTextArg(args, 2, #args)
                        end
                        NewTitle.FontSize = 16
                        NewTitle.FaceCamera = true
                        NewTitle.Name = "PATitle"
                        NewTitle.Parent = player
                        NewTitle.Position = player.Position + Vector3.New(0, 4.5, 0)
                    else
                        player:FindChild('PATitle').Text = GetTextArg(args, 2, #args)
                    end
                end
            else
                CMDError("", Properties.Errors.PlayerNotExist, plr)
            end
        end
    },

    title_remove = {
        cmdtype = "Utility",
        permlv = 1,
        requirements = {
            {Type="string",Required=false}
        },
        usage = "title remove [plr (optional)]",
        enabled = true,
        exe = function(plr, args, msg)
            if not args[1] then args[1] = tostring(plr.Name) end
            local player = GetPlayer(args[1], plr)
            if not player then CMDError("", Properties.Errors.PlayerNotExist, plr) return end

            if player then
                if type(player) == "table" then
                    for i, v in pairs(player) do
                        if v:IsA('Player') then
                            if Properties.UserModelData[v.Name]["HasTitle"] == true then
                                Properties.UserModelData[v.Name]["HasTitle"] = false
                                v:FindChild('PATitle'):Destroy()
                            else
                                CMDError("", "This user does not have a title!", plr)
                            end
                        end
                    end
                else
                    if Properties.UserModelData[player.Name]["HasTitle"] == true then
                        Properties.UserModelData[player.Name]["HasTitle"] = false
                        player:FindChild('PATitle'):Destroy()
                    else
                        CMDError("", "This user does not have a title!", plr)
                    end
                end
            else
                CMDError("", Properties.Errors.PlayerNotExist, plr)
            end
        end
    },

    character = {
        cmdtype = "Utility",
        permlv = 1,
        requirements = {
            {Type="string",Required=true},
            {Type="string",Required=true}
        },
        usage = "character [plr] [user ID]",
        enabled = true,
        exe = function(plr, args, msg)
            local player = GetPlayer(args[1], plr)
            if not player then CMDError("", Properties.Errors.PlayerNotExist, plr) return end

            player:LoadAppearance(tonumber(args[2]))
        end
    },

    clone = {
        cmdtype = "fun",
        permlv = 1,
        requirements = {
            {Type="string",Required=false}
        },
        usage = "clone [plr (optional)]",
        enabled = true,
        exe = function(plr, args, msg)
            if not args[1] then args[1] = tostring(plr.Name) end
            local player = GetPlayer(args[1], plr)
            if not player then CMDError("", Properties.Errors.PlayerNotExist, plr) return end

            local NPC = Instance.New('NPC')
            NPC:LoadAppearance(player.UserID)
            NPC.Position = player.Position
            NPC.Rotation = player.Rotation
        end
    }
}

CmdAliases = {
    ff = CmdDictionary.forcefield,
    unff = CmdDictionary.unforcefield,
    speed = CmdDictionary.walkspeed,
    ws = CmdDictionary.walkspeed,
    jp = CmdDictionary.jumppower,
    thaw = CmdDictionary.unfreeze,
    char = CmdDictionary.character
}

SubCmds = {"whitelist","music","title"}

function IsModelAdmin(plr)
    if Settings.FreeAdmin == true then return true end
    if IsCreator(plr) == true then return true end

    for i, v in pairs(Admins) do
        if v == plr then
            return true
        end
    end
    return false
end

function IsCreator(plr)
    if plr.IsCreator then return true end
    if plr.UserID == 1144 then return true end
    return false
end

function GetPermissionLv(plr)
    if IsCreator(plr) == true then return 2 end
    if IsModelAdmin(plr) == true then return 1 else return 0 end
end

function AreJoinConditionsMet(player, executioner)
    if Bans[player.Name] then
        kickTable = {
            title = "Ban",
            title2 = "banned",
            type = "ban",
            reason = "banned: " .. Bans[player.Name].Reason,
            executionerName = Bans[player.Name].Mod
        }
        Kick(player, kickTable, plr)
    end

    if Settings.WhitelistEnabled == true and not Whitelist[player.Name] then
        kickTable = {
            title = "Kick",
            title2 = "kicked",
            type = "kick",
            reason = "not whitelisted.",
            executionerName = "server"
        }
        Kick(player, kickTable, plr)
    end

    if Settings.ServerlockEnabled == true then
        kickTable = {
            title = "Kick",
            title2 = "kicked",
            type = "kick",
            reason = 'serverlock enabled: "' .. Settings.ServerlockMsg .. '"',
            executionerName = "server"
        }
        Kick(player, kickTable, plr)
    end
    return true
end

function GetTextArg(args, startArg, endArg)
    return table.concat(args, " ", startArg, endArg)
end

function IsBanned(plr)
    for i, v in pairs(Bans) do
        if v.UserID == plr.UserID then
            return true
        end
    end
    return false
end

function IsWhitelisted(plr)
    for i, v in pairs(Whitelist) do
        if v.UserID == plr.UserID then
            return true
        end
    end
    return false
end

function CreatorIsInGame()
    local PlayersTable = Players:GetPlayers()
    for i, v in pairs(PlayersTable) do
        if IsCreator(v) == true then
            return true
        end
    end
    return false
end

function Admin(plr)
    --[[
    adminTableInsert = {
        {
            User = plr.Name,
            ID = plr.UserID
        }
    }
    table.insert(Admins, 1, adminTableInsert[1])
    --]]

    if IsModelAdmin(plr) == true then return end
    table.insert(Admins, 1, plr.UserID)
end

function IsCmdValid(cmd, plr, atts, providedAtts, correctUsage)
    local CmdValidNum = 0
    local CmdValid = true
    for i, v in pairs(atts) do
        CmdValidNum = CmdValidNum + 1
        if v.Required == true then
            --if v.Required == false and providedAtts[CmdValidNum] == nil then print("a1") providedAtts[CmdValidNum] = "[.-NotProvided]" end
            --if v.Type == type(providedAtts[CmdValidNum]) then print("a2") break end
            if not providedAtts[CmdValidNum] then
                CmdValid = false
                CMDError(cmd, "Missing attribute #" .. CmdValidNum .. "!", plr)
                break
            end

            if v.Type == "player" then
                local player = GetPlayer(providedAtts[CmdValidNum], plr)
                if not player then
                    CmdValid = false
                    CMDError(cmd, Properties.Errors.PlayerNotExist, plr)
                    break
                end
            elseif v.Type == "number" then
                if v.Type ~= type(tonumber(providedAtts[CmdValidNum])) then
                    CmdValid = false
                    CMDError(cmd, "That is not a number.", plr)
                    break
                else
                    if v["Min"] and v["Max"] then
                        if tonumber(providedAtts[CmdValidNum]) >= v["Min"] and tonumber(providedAtts[CmdValidNum]) <= v["Max"] then
                            providedAtts[CmdValidNum] = tonumber(providedAtts[CmdValidNum])
                        else
                            CmdValid = false
                            CMDError(cmd, "Number provided must be a minimum of " .. atts[CmdValidNum].Min .. " and a maximum of " .. atts[CmdValidNum].Max .. "!", plr)
                            break
                        end
                    else
                        providedAtts[CmdValidNum] = tonumber(providedAtts[CmdValidNum])
                    end
                end
            elseif v.Type == "bool" then
                CmdValid3 = false
                CmdValid4 = ""
                for i, v in pairs(Properties.BoolValues) do
                    if providedAtts[CmdValidNum] == v then
                        CmdValid3 = true
                        CmdValid4 = v
                    end
                end

                if CmdValid3 == false then
                    CmdValid = false
                    CMDError(cmd, "That is not a valid bool operator.", plr)
                    break
                else
                    providedAtts[CmdValidNum] = CmdValid4
                end
            elseif v.Type == "admin" or v.Type == "nonadmin" then
                local player = GetPlayer(providedAtts[CmdValidNum], plr)

                if player then
                    if v.Type == "admin" then
                        if IsModelAdmin(player) == false then
                            CmdValid = false
                            CMDError(cmd, Properties.Errors.PlrNeedsToBeAdmin, plr)
                            break
                        end
                    else
                        if IsModelAdmin(player) == true then
                            CmdValid = false
                            CMDError(cmd, Properties.Errors.PlrNeedsToBeNonAdmin, plr)
                            break
                        end
                    end
                else
                    CmdValid = false
                    CMDError(cmd, Properties.Errors.PlayerNotExist, plr)
                    break
                end
            elseif v.Type ~= type(providedAtts[CmdValidNum]) then
                CmdValid = false
                CMDError(cmd, "Provided type is invalid.", plr)
                break
            end
        end
    end

    if CmdValid == false then
        CMDError(cmd, 'Invalid Usage. Correct Usage: "' .. correctUsage .. '".', plr)
    end
    return CmdValid
    --return {value = CmdValid, newAtts = providedAtts}
end

function GetPlayer(plrString, exe)
    if plrString == nil or plrString == "" then return end

    if string.find(plrString, ",") and IsCreator(exe) then
        local splitBySep = SplitString(plrString, ",")
        GetPlayersTable = {}

        for i, v in pairs(splitBySep) do
            player = GetPlayer(v, exe)
            if player then
                table.insert(GetPlayersTable, player)
            end
        end

        return GetPlayersTable
    else
        if string.sub(plrString, 1, 1) ~= "*" then
            local PlayersTable = Players:GetPlayers()
            for i, v in pairs(PlayersTable) do
                if string.find(string.lower(v.Name), string.lower(plrString)) then
                    return v
                end
            end
        else
            if plrString == "*me" then
                return exe
            elseif plrString == "*a" or plrString == "*all" then
                local PlayersTable = Players:GetPlayers()
                return PlayersTable
            elseif plrString == "*others" then
                local PlayersTable = Players:GetPlayers()
                for i, v in pairs(PlayersTable) do
                    if v == exe then
                        PlayersTable[v] = nil
                    end
                end
                return PlayersTable
            elseif plrString == "*admins" then
                local PlayersTable = Players:GetPlayers()
                for i, v in pairs(PlayersTable) do
                    if IsModelAdmin(v) == false then
                        PlayersTable[v] = nil
                    end
                end
                return PlayersTable
            elseif plrString == "*nonadmins" then
                local PlayersTable = Players:GetPlayers()
                for i, v in pairs(PlayersTable) do
                    if IsModelAdmin(v) == true then
                        PlayersTable[v] = nil
                    end
                end
                return PlayersTable
            elseif plrString == "*r" or plrString == "*random" then
                local PlayersTable = Players:GetPlayers()
                return PlayersTable[math.random(1, #PlayersTable)]
            end
        end
    end
end

function ResetPlayer(plr)
    if type(plr) == "table" then
        for i, v in pairs(plr) do
            if v:IsA('Player') then
                v.MaxHealth = PlayerDefaults.MaxHealth
                v.WalkSpeed = PlayerDefaults.WalkSpeed
                v.SprintSpeed = PlayerDefaults.SprintSpeed
                v.StaminaEnabled = PlayerDefaults.StaminaEnabled
                v.Stamina = PlayerDefaults.Stamina
                v.MaxStamina = PlayerDefaults.MaxStamina
                v.StaminaRegen = PlayerDefaults.StaminaRegen
                v.JumpPower = PlayerDefaults.JumpPower
                v.RespawnTime = PlayerDefaults.RespawnTime
            end
        end
    else
        plr.MaxHealth = PlayerDefaults.MaxHealth
        plr.WalkSpeed = PlayerDefaults.WalkSpeed
        plr.SprintSpeed = PlayerDefaults.SprintSpeed
        plr.StaminaEnabled = PlayerDefaults.StaminaEnabled
        plr.Stamina = PlayerDefaults.Stamina
        plr.MaxStamina = PlayerDefaults.MaxStamina
        plr.StaminaRegen = PlayerDefaults.StaminaRegen
        plr.JumpPower = PlayerDefaults.JumpPower
        plr.RespawnTime = PlayerDefaults.RespawnTime
    end
end

function Kick(plr, info, executioner)
    if type(plr) == "table" then
        if info.type == "kick" then
            for i, v in pairs(plr) do
                v:Kick('<align=center><size=20><size=50><sprite=15></size><br>' .. info.title .. '<br><size=15><alpha=#99>You have been ' .. info.title2 .. ' from this server!<br><br><b><color=#fff>Disconnect Information</color></b><br><alpha=#99>Reason: "' .. info.reason .. '"<br>Moderator: ' .. info.executionerName .. '</size></size></align>')
            end
        else
            for i, v in pairs(plr) do
                v:Kick('<align=center><size=20><size=50><sprite=15></size><br>' .. info.title .. '<br><size=15><alpha=#99>You have been ' .. info.title2 .. ' from this server!<br><br><b><color=#fff>Disconnect Information</color></b><br><alpha=#99>Ban Length: ' .. 'until unban' .. '<br>Reason: "' .. info.reason .. '"<br>Moderator: ' .. info.executionerName .. '</size></size></align>')
            end
        end
    else
        if info.type == "kick" then
            plr:Kick('<align=center><size=20><size=50><sprite=15></size><br>' .. info.title .. '<br><size=15><alpha=#99>You have been ' .. info.title2 .. ' from this server!<br><br><b><color=#fff>Disconnect Information</color></b><br><alpha=#99>Reason: "' .. info.reason .. '"<br>Moderator: ' .. info.executionerName .. '</size></size></align>')
        else
            plr:Kick('<align=center><size=20><size=50><sprite=15></size><br>' .. info.title .. '<br><size=15><alpha=#99>You have been ' .. info.title2 .. ' from this server!<br><br><b><color=#fff>Disconnect Information</color></b><br><alpha=#99>Ban Length: ' .. 'nil' .. '<br>Reason: "' .. info.reason .. '"<br>Moderator: ' .. info.executionerName .. '</size></size></align>')
        end
    end
end

function GUI(gui, plr, guitype, AdditionalInfo, plrType)
    local newNetMsg = NetMessage.New()
    newNetMsg.AddString("gui", gui)
    newNetMsg.AddString("type", guitype)

    if AdditionalInfo then
        for i, v in pairs(AdditionalInfo) do
            if v.Type == "string" then
                newNetMsg.AddString(v.Key, v.Value)
            elseif v.Type == "number" then
                newNetMsg.AddNumber(v.Key, v.Value)
            elseif v.Type == "bool" then
                newNetMsg.AddBool(v.Key, v.Value)
            elseif v.Type == "instance" then
                newNetMsg.AddInstance(v.Key, v.Value)
            end
        end
    end

    if plrType == "plr" then
        if type(plr) == "table" then
            for i, v in pairs(plr) do
                if v:IsA('Player') then
                    script.Parent["GUIUpdate"].InvokeClient(newNetMsg, v)
                end
            end
        else
            if plr:IsA('Player') then
                script.Parent["GUIUpdate"].InvokeClient(newNetMsg, plr)
            end
        end
    elseif plrType == "string" then
        if plr == "all" then
            script.Parent["GUIUpdate"].InvokeClients(newNetMsg)
        end
    end
end

function CMDInfo(command, string, plr)
    if command ~= "" then
        Chat:UnicastMessage("<color=#f2f2f2>[" .. Settings.Prefix .. command .. "] " .. string .. "</color>", plr)
    else
        Chat:UnicastMessage("<color=#f2f2f2>" .. string, plr)
    end
end

function CMDSuccess(command, string, plr)
    if command ~= "" then
        Chat:UnicastMessage("<color=#4d9b08>[" .. Settings.Prefix .. command .. "] " .. string .. "</color>", plr)
    else
        Chat:UnicastMessage("<color=#4d9b08>" .. string, plr)
    end
end

function CMDError(command, string, plr)
    if command ~= "" then
        Chat:UnicastMessage("<color=#e70808>[" .. Settings.Prefix .. command .. "] " .. string .. "</color>", plr)
    else
        Chat:UnicastMessage("<color=#e70808>" .. string, plr)
    end
end

script.Parent["ExecuteCommand"].InvokedServer:Connect(function (plr, netMsg)
    ExecuteCMD(netMsg.GetString("msg"), plr)
end)

function FindInTable(table, index)
    for i, v in pairs(table) do
        if v == index then
            return {i,true}
        end
    end
    return {nil,false}
end

function ConcatTable(table, sep)
    newConcat = ""
    if not sep then sep = "," end
    if sep == "[noSep]" then sep = " " end

    for i, v in pairs(table) do
        if v ~= table[1] then
            newConcat = newConcat .. sep .. v
        else
            newConcat = v
        end
    end

    return newConcat
end

-- Credits to StackOverflow for the "SplitString" function
function SplitString(inputstr, sep)
    if sep == nil then
       sep = "%s"
    end
    local t={}
    for str in string.gmatch(inputstr, "([^"..sep.."]+)") do 
       table.insert(t, str)
    end
    return t
end