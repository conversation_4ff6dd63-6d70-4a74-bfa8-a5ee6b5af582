local TestingMode = true
print("hi")

local Datastore = require(script.Parent:FindChild("DataHandler"))
local PlayerData = game["Environment"]:FindChild("PlayerData")
print("hello")

game["Players"].PlayerAdded:Connect(function(Player)
    print("player added")
    local Data = Instance.New("Folder")
    Data.Name = Player.Name
    local KillValue = Instance.New("IntValue")
    KillValue.Name = "Kills"
    KillValue.Parent = Data
    local DeathValue = Instance.New("IntValue")
    DeathValue.Name = "Deaths"
    DeathValue.Parent = Data
    Data.Parent = PlayerData
    print("LOAD")
    local data = Datastore:LoadPlayerData(Player) --{Kills = 0; Deaths = 0;}
    print("loaded")
    if not data then warn("Could not load save data for " .. Player.Name) end
    if data ~= nil then
        print(data)
        print(data.Kills)
        print(data.Deaths)
        KillValue.Value = data.Kills
        DeathValue.Value = data.Deaths
    end
    wait(2)
    Achievements:Award(Player.UserID, 52439, function(success, error)
        if success then
            print("Awarded welcome achievement")
        else
            print("Error awarding welcome achievement: " .. error)
        end
    end)
end)

game["Players"].PlayerRemoved:Connect(function(Player)
    local Data = PlayerData:FindChild(Player.Name)
    if Data then
        local success = Datastore:SavePlayerData(Player, {
            Kills = Data:FindChild("Kills").Value;
            Deaths = Data:FindChild("Deaths").Value;
        })
        if not success then warn("Could not save save data for " .. Player.Name) end
    end
end)

local KillSignal = game["Hidden"]:FindChild("Kill")

local KillMessages = {
    "%s killed %s!";
    "%s > %s";
    "%s showed %s who's boss!";
    "%s's blade was too much for %s!";
    "%s sliced %s to pieces!";
    "%s overpowered %s!";
}

KillSignal.Invoked:Connect(function(Killer, Killed)
    Chat:BroadcastMessage(string.format(KillMessages[math.random(1, #KillMessages)], Killer.Name, Killed.Name))
    local KillerData = PlayerData:FindChild(Killer.Name)
    KillerData:FindChild("Kills").Value = KillerData:FindChild("Kills").Value + 1
    if Killed:IsA("Player") then
        local KilledData = PlayerData:FindChild(Killed.Name)
        KilledData:FindChild("Deaths").Value = KilledData:FindChild("Deaths").Value + 1
    end
    print(Killer.Name.." now has "..KillerData:FindChild("Kills").Value.." kills!")
end)