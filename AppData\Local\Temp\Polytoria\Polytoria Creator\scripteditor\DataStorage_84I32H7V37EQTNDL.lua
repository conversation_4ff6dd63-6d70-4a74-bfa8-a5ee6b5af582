function DataStorage(dbAddress, options)
    local ds = {
        dbAd = dbAddress,
    }

    local atKey = ""

    if options['authKey'] then
        atKey = "?auth="..options['authKey']
    end

    function ds.Read(path, callback)
        Http:Get("https://"..ds.dbAd.."/"....path..".json"..atKey, function (data, err, errmsg)
            if callback then
                if err == false then
                    callback(json.parse(data))
                else
                    callback(nil, data, errmsg)
                end
            end
        end, {})
    end

    local function curT()
        return os.time(os.date("!*t"))
    end

    function ds.Set(path, ndata, callback)
        local preD = ndata
        preD['lastUpdated'] = curT()

        Http:Put("https://"..ds.dbAd.."/"....path..".json"..atKey, json.serialize(preD), function (data, err, errmsg)
            if callback then
                if err == false then
                    callback(true)
                else
                    callback(false, data, errmsg)
                end
            end
        end, {})
    end

    function ds.Update(path, ndata, callback)
        local preD = ndata
        preD['lastUpdated'] = curT()

        Http:Post("https://"..ds.dbAd.."/"....path..".json"..atKey, json.serialize(preD), function (data, err, errmsg)
            if callback then
                if err == false then
                    callback(true)
                else
                    callback(false, data, errmsg)
                end
            end
        end, {
            ["X-HTTP-Method-Override"] = "PATCH"
        })
    end

    function ds.Push(path, ndata, callback)
        local preD = ndata
        preD['lastUpdated'] = curT()

        Http:Post("https://"..ds.dbAd.."/"....path..".json"..atKey, json.serialize(preD), function (data, err, errmsg)
            if callback then
                if err == false then
                    callback(true)
                else
                    callback(false, data, errmsg)
                end
            end
        end, {})
    end

    function ds.Delete(path, ndata, callback)
        local preD = ndata
        preD['lastUpdated'] = curT()

        Http:Delete("https://"..ds.dbAd.."/"....path..".json"..atKey, json.serialize(preD), function (data, err, errmsg)
            if callback then
                if err == false then
                    callback(true)
                else
                    callback(false, data, errmsg)
                end
            end
        end, {})
    end

    local function runSchema(sch, da, callback, ignore)
        local data = da
        local schD = sch.model

        for i,v in pairs(data) do
            if schD[i] == nil then
                if ignore then
                    goto skipchecks
                end
                callback(false, tostring(i).." key does not exist.")
                return
            else
                local bp = schD[i]

                if bp['varType'] and bp['varType'] ~= "any" then
                    if bp['varType'] ~= type(v) and ignore == false then
                        callback(false, tostring(i).." 's type does not match")
                        return
                    end
                end
    
                if type(v) == "string" then
                    if bp['lowercase'] then
                        v = string.lower(v)
                    end
    
                    if bp['uppercase'] then
                        v = string.upper(v)
                    end
    
                    local strL = string.len(v)
    
                    if bp['minLength'] then
                        if strL < bp['minLength'] and ignore == false then
                            callback(false, tostring(i).." 's value is too short")
                            return
                        end
                    end
    
                    if bp['maxLength'] then
                        if strL > bp['maxLength'] and ignore == false then
                            callback(false, tostring(i).." 's value is too long")
                            return
                        end
                    end
                end

                data[i] = v

            end

            ::skipchecks::
        end

        --[[
            -- required feature, disabled
        for i,v in pairs(schD) do
            if v.required == true and igreq == false then
                if data[i] == nil then
                    callback(false, tostring(i).." is missing.")
                    return
                end
            end
        end
        ]]

        callback(true, data)
    end

    function ds.Schema(model, options)
        local sch = {
            model = model,
            path = options['path'] or ""
        }

        function sch.Set(keyName, ndata, callback)
            runSchema(sch, ndata, function(success, data)
                if success == false then
                    if callback then
                        callback(false, data)
                    end
                else
                    ds.Set(sch.path..'/'..keyName, data, function(succ, d, erms)
                        if callback then
                            callback(succ, d, erms)
                        end
                    end)
                end
            end, false)
        end

        function sch.Update(keyName, ndata, callback)
            runSchema(sch, ndata, function(success, data)
                if success == false then
                    if callback then
                        callback(false, data)
                    end
                else
                    ds.Update(sch.path..'/'..keyName, data, function(succ, d, erms)
                        if callback then
                            callback(succ, d, erms)
                        end
                    end)
                end
            end, false)
        end

        function sch.Push(ndata, callback)
            runSchema(sch, ndata, function(success, data)
                if success == false then
                    if callback then
                        callback(false, data)
                    end
                else
                    ds.Push(sch.path, data, function(succ, d, erms)
                        if callback then
                            callback(succ, d, erms)
                        end
                    end)
                end
            end, false)
        end

        function sch.Read(keyName, callback)
            ds.Read(sch.path..'/'..keyName, function(d, erms, erc)
                if d == nil then
                    if callback then
                        callback(false, erms, erc)
                    end
                    return
                end

                runSchema(sch, d, function(success, data)
                    if success == false then
                        if callback then
                            callback(false, data, erms)
                        end
                    else
                        if callback then
                            callback(true, d)
                        end
                    end
                end, true)
            end)
        end

        return sch
    end

    return ds
end

-----------------------------------------

wait(1)

-- put full link of your database here
local ds = DataStorage("", {
})

--[[
-- usual fetch
ds.Set("Fr", {
    value = math.random(1,10)
}, function()
    ds.Read("Fr", function(res, err, errmsg)
        print(res.value)
    end)
end)
]]

local newModel = ds.Schema({
    username = {
        varType = "string",
        lowercase = true,
        trim = true,
        --uppercase = true,
        --minLength = 0,
        --maxLength = 20,
    },
    userid = {
        varType = "number",
    }
}, {path = "Players"})

-- write data
newModel.Set("1", {
    username = "Player1",
    userid = 2
})

wait(1)

-- update the data, userid will not be changed
newModel.Update("1", {
    username = "ChangedUser1",
})

wait(1)

-- create another
newModel.Set("Joe", {
    username = "AAAAAAAAAAA",
    userid = 69
})

wait(1)

-- create new data with random unique id
newModel.Push({
    username = "RandomUser",
    userid = 8787
})

wait(1)

-- read data
newModel.Read("1", function(suc, da)
    print(json.serialize(da))
end)