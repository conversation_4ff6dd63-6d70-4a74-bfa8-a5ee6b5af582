local API_BASE_URL = "https://api-testing-iota-one.vercel.app"
local GAME_ID = game.GameID

local PlayerDataManager = {}
PlayerDataManager.LoadedPlayers = {}

local DEFAULT_PLAYER_DATA = {
    Kills = 0;
    Deaths = 0;
    IsBanned = false
}

local function Retry(funcToRetry, callBackFunction, triesLeft, args)
    local success, result = pcall(function()
        return funcToRetry(table.unpack(args))
    end)
    
    if success then
        return success, result
    else
        if triesLeft == 0 then
            return false, nil
        end
        callBackFunction(result, triesLeft)
        triesLeft = triesLeft - 1
        Retry()
    end
end

local function makeAPIRequest(method, endpoint, data)
    local url = API_BASE_URL .. endpoint
    local success, result = Retry(function()
        if method == "GET" then
            return Http:Get(url)
        elseif method == "POST" then
            return Http:Post(url, function (data, error, errmsg)
                if error then warn(errmsg) end
                return json.parse(data)
            end)
        elseif method == "PUT" then
            return Http:Put({
                Url = url,
                Method = "PUT",
                Headers = {["Content-Type"] = "application/json"},
                Body = json.parse(data)
            }).Body
        elseif method == "DELETE" then
            return Http:Delete({
                Url = url,
                Method = "DELETE"
            }).Body
        end
    end, function(result, triesLeft)
        warn("API Request failed: " .. tostring(result) .. " Retrying in 1 second. Tries left: " .. triesLeft)
    end, 5, {method; endpoint; data})
    
    if success then
        return result
    else
        warn("API Request failed: " .. tostring(result))
        return nil
    end
end

function PlayerDataManager:Reconcile(player, playerData)
    local TargetData = DEFAULT_PLAYER_DATA
    if PlayerDataManager.LoadedPlayers[player] then TargetData = PlayerDataManager.LoadedPlayers[player] end
    for key, value in pairs(TargetData) do
        if playerData[key] == nil then
            playerData[key] = value
        end
    end
    return playerData
end

function PlayerDataManager:LoadPlayerData(player)
    local userId = tostring(player.UserId)
    local endpoint = "/games/" .. GAME_ID .. "/users/" .. userId
    local response = makeAPIRequest("GET", endpoint)
    
    if response and response.success then
        local playerData = response.data
        if playerData.IsBanned then
            player:Kick("You have been banned from all games owned by @Sweaty [UserId:60647]")
            return nil
        end
        playerData = self:Reconcile(player, playerData)
        self.LoadedPlayers[userId] = playerData
        return playerData
    else
        return self:CreateDefaultPlayerData(player)
    end
end

function PlayerDataManager:CreateDefaultPlayerData(player)
    local userId = tostring(player.UserId)
    local defaultData = {}
    
    for key, value in pairs(DEFAULT_PLAYER_DATA) do
        if type(value) == "table" then
            defaultData[key] = {}
            for k, v in pairs(value) do
                defaultData[key][k] = v
            end
        else
            defaultData[key] = value
        end
    end
    
    local success = self:SavePlayerData(player, defaultData)
    if success then
        self.LoadedPlayers[userId] = defaultData
        return defaultData
    else
        warn("Failed to create default data for " .. player.Name)
        return nil
    end
end

function PlayerDataManager:SavePlayerData(player, data)
    local userId = tostring(player.UserId)
    data = self:Reconcile(player, data)
    local endpoint = "/games/" .. GAME_ID .. "/users/" .. userId
    local response = makeAPIRequest("POST", endpoint, data)
    
    if response and response.success then
        return true
    else
        warn("Failed to save data for " .. player.Name)
        return false
    end
end

function PlayerDataManager:UpdatePlayerData(player, updates)
    local userId = tostring(player.UserId)
    
    if not self.LoadedPlayers[userId] then
        warn("Player data not loaded for " .. player.Name)
        return false
    end
    
    for key, value in pairs(updates) do
        self.LoadedPlayers[userId][key] = value
    end
    
    local endpoint = "/games/" .. GAME_ID .. "/users/" .. userId
    local response = makeAPIRequest("PUT", endpoint, updates)
    
    if response and response.success then
        return true
    else
        warn("Failed to update data for " .. player.Name)
        return false
    end
end

function PlayerDataManager:GetPlayerData(player)
    local userId = tostring(player.UserId)
    return self.LoadedPlayers[userId]
end

function PlayerDataManager:SetPlayerBanStatus(player, isBanned)
    local userId = tostring(player.UserId)
    local endpoint = "/games/" .. GAME_ID .. "/users/" .. userId
    local response = makeAPIRequest("PUT", endpoint, {IsBanned = isBanned})
    
    if response and response.success then
        if isBanned then
            player:Kick("You have been banned from this game.")
        end
        return true
    else
        warn("Failed to " .. (isBanned and "ban" or "unban") .. " player: " .. player.Name)
        return false
    end
end

function PlayerDataManager:CleanupPlayer(player)
    local userId = tostring(player.UserId)
    if self.LoadedPlayers[userId] then
        self.LoadedPlayers[userId] = nil
    end
end

--[[
game["Players"].PlayerAdded:Connect(function(Player)
    spawn(function()
        local playerData = PlayerDataManager:LoadPlayerData(Player)
        
        if playerData then
            local leaderstats = Instance.new("Folder")
            leaderstats.Name = "leaderstats"
            leaderstats.Parent = Player
            
            local level = Instance.new("IntValue")
            level.Name = "Level"
            level.Value = playerData.level
            level.Parent = leaderstats
            
            local score = Instance.new("IntValue")
            score.Name = "Score"
            score.Value = playerData.score
            score.Parent = leaderstats
        end
    end)
end)

game["Players"].PlayerRemoving:Connect(function(Player)
    spawn(function()
        local playerData = PlayerDataManager:GetPlayerData(Player)
        if playerData then
            local leaderstats = Player:FindFirstChild("leaderstats")
            if leaderstats then
                local level = leaderstats:FindFirstChild("Level")
                local score = leaderstats:FindFirstChild("Score")
                
                if level then playerData.level = level.Value end
                if score then playerData.score = score.Value end
                
                PlayerDataManager:SavePlayerData(Player, playerData)
            end
        end
        
        PlayerDataManager:CleanupPlayer(Player)
    end)
end)
--]]

return PlayerDataManager
