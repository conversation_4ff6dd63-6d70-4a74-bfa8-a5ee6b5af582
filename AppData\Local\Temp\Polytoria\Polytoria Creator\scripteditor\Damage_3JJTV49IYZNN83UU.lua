--[[
Linked_Sword.lua
Author: <PERSON><PERSON><PERSON><PERSON>
--]]

-- References
local Tool = script.Parent
local Players = game["Players"] -- A service to get player information
local KillSignal = game["Hidden"]:FindChild("Kill")
local SwingSignal = Tool:FindChild("Swing")

local Sounds = Tool:FindChild("Sounds")
local Handle = Tool:FindChild("Handle")
local Hitbox = Handle:FindChild("Hitbox")

local Debounce = false -- Debounce variable for the sword swing
local ClickRate = 0
local DamageOnHit = 40
local isDouble = false

-- A function to handle the damage logic when a target is hit.
function damageTarget(target)
    -- Get the player that owns the tool
    local swordOwner = Tool.Parent
    if not swordOwner:IsA("Player") then
        return
    end

    -- Check if the target is a player
    local DamageToDeal = DamageOnHit
    if isDouble then DamageToDeal = DamageOnHit * 2 end
    if target:IsA("Player") or target:IsA("NPC") then
        if target.Health > 0 and (target.Health - DamageToDeal) <= 0 then --Kill confirmation
            KillSignal:Invoke(swordOwner, target)
        end
    end

    if target:<PERSON><PERSON>("Player") then
        if target == swordOwner then
            return -- Don't hit yourself
        end
        -- We can directly access the Health property on the Player and damage them.
        if isDouble then
            target.Health = target.Health - (DamageOnHit * 2)
        else
            target.Health = target.Health - DamageOnHit
        end
    -- Check if the target is an NPC
    elseif target:IsA("NPC") then
        -- We can directly access the Health property on the NPC.
        -- We'll check if it exists before trying to modify it.
        if target.Health then
            if isDouble then
                target.Health = target.Health - (DamageOnHit * 2)
            else
                target.Health = target.Health - DamageOnHit
            end
        end
    end

    --[[ Apply a damage cooldown to the hit target
    recentlyDamaged[target] = true
    wait(0.1) -- Adjust the cooldown time as needed
    recentlyDamaged[target] = nil
    --]]
end

-- Functions
function swingAnimation(isDouble)
    if Debounce == true then
        return
    end

    -- Check for the ultimate attack combo
    if ClickRate > 2 then
        isDouble = math.random(1, 3) == 1
        
        if isDouble == true then
            if Sounds and Sounds:FindChild("Lunge") then
                Sounds:FindChild("Lunge"):Play()
                local bodyPosition = Instance.New("BodyPosition")
                bodyPosition.Parent = Tool.Parent
                bodyPosition.TargetPosition = Tool.Parent.Position + Vector3.new(0, 10, 0)
                bodyPosition.AcceptanceDistance = 0.1
                bodyPosition.Force = 1.5
                Handle.LocalRotation = Vector3.new(90, 0, 0)
                wait(0.5)
                Handle.LocalRotation = Vector3.new(0, 0, 0)
                bodyPosition:Destroy()
            end
            ClickRate = 0 -- Reset ClickRate after a successful lunge
            isDouble = false
        else
            Tool:Play("slash")
        end
    else
        Tool:Play("slash")
    end
end

function swingOnce()
    if Debounce == true then
        return
    end

    Debounce = true -- Set debounce at the start of the function

    ClickRate = ClickRate + 1

    if ClickRate > 2 then
        isDouble = math.random(1,3) == 1
    else
        isDouble = false
    end

    SwingSignal:Invoke(isDouble)

    if Sounds and Sounds:FindChild("Slash") then
        Sounds:FindChild("Slash"):Play()
    end

    if isDouble then
        wait(0.5)
        ClickRate = 0
    end
    wait(0.2)
    isDouble = false
    Debounce = false
end

function onHitboxTouched(Hit)
    
    -- Start from the part that was hit and check its parents until a Player or NPC is found.
    local character = Hit
    while character and not character:IsA("Player") and not character:IsA("NPC") do
        character = character.Parent
    end

    -- If no valid character was found, or if it's the sword's owner, exit the function.
    if not character or character == Tool.Parent then
        return
    end

    -- Call the damage function with the found character.
    damageTarget(character)
end

function Equipped()
    if Sounds and Sounds:FindChild("Unsheath") then
        Sounds:FindChild("Unsheath"):Play()
    end
    Handle.LocalRotation = Vector3.new(0, 0, 0)
end

if Tool.Parent:IsA("Player") then
    Equipped()
end

Tool.Equipped:Connect(Equipped)
Tool.Activated:Connect(swingOnce)
Handle.Touched:Connect(onHitboxTouched)