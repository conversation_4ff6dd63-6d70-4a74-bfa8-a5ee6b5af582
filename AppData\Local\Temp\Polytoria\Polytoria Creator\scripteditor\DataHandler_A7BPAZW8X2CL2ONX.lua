local API_BASE_URL = "https://api-testing-iota-one.vercel.app"
local GAME_ID = tostring(game.GameID)

local PlayerDataManager = {}
PlayerDataManager.LoadedPlayers = {}

local DEFAULT_PLAYER_DATA = {
    Kills = 0;
    Deaths = 0;
    IsBanned = false
}

function PlayerDataManager:Reconcile(player, playerData)
    local TargetData = DEFAULT_PLAYER_DATA
    if PlayerDataManager.LoadedPlayers[player] then TargetData = PlayerDataManager.LoadedPlayers[player] end
    for key, value in pairs(TargetData) do
        if playerData[key] == nil then
            playerData[key] = value
        end
    end
    return playerData
end

function PlayerDataManager:LoadPlayerData(player)
    local userId = tostring(player.UserID)
    local endpoint = "/games/" .. GAME_ID .. "/users/" .. userId
    local url = tostring(API_BASE_URL .. endpoint)

    local result = Http:Get(url, function(data, error, errmsg)
        if error then
            warn("HTTP Error: " .. tostring(errmsg))
            return nil
        end
        return json.parse(data)
    end, {})

    if result ~= nil and result.data then
        local playerData = result.data
        if playerData.IsBanned then
            player:Kick("You have been banned from all games owned by @Sweaty [UserId:60647]")
            return nil
        end
        playerData = self:Reconcile(player, playerData)
        self.LoadedPlayers[userId] = playerData
        return playerData
    else
        return self:CreateDefaultPlayerData(player)
    end
end

function PlayerDataManager:CreateDefaultPlayerData(player)
    local userId = tostring(player.UserID)
    local defaultData = {}
    
    for key, value in pairs(DEFAULT_PLAYER_DATA) do
        if type(value) == "table" then
            defaultData[key] = {}
            for k, v in pairs(value) do
                defaultData[key][k] = v
            end
        else
            defaultData[key] = value
        end
    end
    
    local success = self:SavePlayerData(player, defaultData)
    if success then
        self.LoadedPlayers[userId] = defaultData
        return defaultData
    else
        warn("Failed to create default data for " .. player.Name)
        return nil
    end
end

function PlayerDataManager:SavePlayerData(player, data)
    local userId = tostring(player.UserID)
    data = self:Reconcile(player, data)
    local endpoint = "/games/" .. GAME_ID .. "/users/" .. userId
    local url = tostring(API_BASE_URL .. endpoint)

    local result = Http:Post(url, json.serialize(data), function(responseData, error, errmsg) 
        if not error then 
            return json.parse(responseData) 
        else 
            warn("HTTP Error: " .. tostring(errmsg)) 
            return nil 
        end 
    end, {})

    if result ~= nil and result.success then
        return true
    else
        warn("Failed to save data for " .. player.Name)
        return false
    end
end

function PlayerDataManager:UpdatePlayerData(player, updates)
    local userId = tostring(player.UserID)

    if not self.LoadedPlayers[userId] then
        warn("Player data not loaded for " .. player.Name)
        return false
    end

    for key, value in pairs(updates) do
        self.LoadedPlayers[userId][key] = value
    end

    local endpoint = "/games/" .. GAME_ID .. "/users/" .. userId
    local url = tostring(API_BASE_URL .. endpoint)

    local result = Http:Put(url, json.serialize(updates), function(responseData, error, errmsg)
        if not error then
            return json.parse(responseData)
        else
            warn("HTTP Error: " .. tostring(errmsg))
            return nil
        end
    end, {})

    if result ~= nil and result.success then
        return true
    else
        warn("Failed to update data for " .. player.Name)
        return false
    end
end

function PlayerDataManager:GetPlayerData(player)
    local userId = tostring(player.UserID)
    return self.LoadedPlayers[userId]
end

function PlayerDataManager:SetPlayerBanStatus(player, isBanned)
    local userId = tostring(player.UserID)
    local endpoint = "/games/" .. GAME_ID .. "/users/" .. userId
    local url = tostring(API_BASE_URL .. endpoint)
    local banData = {IsBanned = isBanned}

    local result = Http:Put(url, json.serialize(banData), function(responseData, error, errmsg)
        if not error then
            return json.parse(responseData)
        else
            warn("HTTP Error: " .. tostring(errmsg))
            return nil
        end
    end, {})

    if result ~= nil and result.success then
        if isBanned then
            player:Kick("You have been banned from this game.")
        end
        return true
    else
        warn("Failed to " .. (isBanned and "ban" or "unban") .. " player: " .. player.Name)
        return false
    end
end

function PlayerDataManager:CleanupPlayer(player)
    local userId = tostring(player.UserID)
    if self.LoadedPlayers[userId] then
        self.LoadedPlayers[userId] = nil
    end
end

--[[
game["Players"].PlayerAdded:Connect(function(Player)
    spawn(function()
        local playerData = PlayerDataManager:LoadPlayerData(Player)
        
        if playerData then
            local leaderstats = Instance.new("Folder")
            leaderstats.Name = "leaderstats"
            leaderstats.Parent = Player
            
            local level = Instance.new("IntValue")
            level.Name = "Level"
            level.Value = playerData.level
            level.Parent = leaderstats
            
            local score = Instance.new("IntValue")
            score.Name = "Score"
            score.Value = playerData.score
            score.Parent = leaderstats
        end
    end)
end)

game["Players"].PlayerRemoving:Connect(function(Player)
    spawn(function()
        local playerData = PlayerDataManager:GetPlayerData(Player)
        if playerData then
            local leaderstats = Player:FindFirstChild("leaderstats")
            if leaderstats then
                local level = leaderstats:FindFirstChild("Level")
                local score = leaderstats:FindFirstChild("Score")
                
                if level then playerData.level = level.Value end
                if score then playerData.score = score.Value end
                
                PlayerDataManager:SavePlayerData(Player, playerData)
            end
        end
        
        PlayerDataManager:CleanupPlayer(Player)
    end)
end)
--]]

return PlayerDataManager